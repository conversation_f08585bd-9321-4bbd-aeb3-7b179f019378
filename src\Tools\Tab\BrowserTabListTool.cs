// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Tools.Tab;

/// <summary>
/// Tool for listing browser tabs.
/// </summary>
public class BrowserTabListTool : PlaywrightToolBase
{
    public override ToolDefinition Definition => new()
    {
        Name = "browser_tab_list",
        Title = "List tabs",
        Description = "List browser tabs",
        IsReadOnly = true,
        Capability = ToolCapability.Tabs,
        InputSchema = CreateJsonSchema(new Dictionary<string, object>())
    };

    public override ToolCapability Capability => ToolCapability.Tabs;

    public override async Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters)
    {
        var tabsMarkdown = await context.BrowserManager.GetTabsMarkdownAsync();

        var result = new List<string>
        {
            "- Ran Playwright code: ```js",
            "// List all browser tabs",
            "const pages = context.pages();",
            "```",
            "",
            tabsMarkdown
        };

        return CreateTextResult(string.Join("\n", result));
    }
}
