// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.Text.Json;
using Microsoft.Extensions.Logging;
using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;
using PlaywrightMcp.Tools;

namespace PlaywrightMcp.Server;

/// <summary>
/// Handles MCP requests and routes them to appropriate tools.
/// </summary>
public class McpRequestHandler : IAsyncDisposable
{
    private readonly McpConfig _config;
    private readonly ILogger _logger;
    private readonly ToolRegistry _toolRegistry;
    private readonly BrowserManager _browserManager;

    public McpRequestHandler(McpConfig config, ILogger logger)
    {
        _config = config;
        _logger = logger;
        _browserManager = new BrowserManager(config, logger);
        _toolRegistry = new ToolRegistry(config, _browserManager, logger);
    }

    /// <summary>
    /// Handles an MCP request and returns the appropriate response.
    /// </summary>
    public async Task<McpResponse> HandleRequestAsync(McpRequest request)
    {
        try
        {
            return request.Method switch
            {
                "tools/list" => await <PERSON>leListToolsAsync(request),
                "tools/call" => await HandleCallToolAsync(request),
                "initialize" => HandleInitialize(request),
                _ => new McpResponse
                {
                    Id = request.Id,
                    Error = new McpError
                    {
                        Code = -32601,
                        Message = $"Method not found: {request.Method}"
                    }
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling request {Method}", request.Method);
            return new McpResponse
            {
                Id = request.Id,
                Error = new McpError
                {
                    Code = -32603,
                    Message = "Internal error",
                    Data = ex.Message
                }
            };
        }
    }

    private McpResponse HandleInitialize(McpRequest request)
    {
        return new McpResponse
        {
            Id = request.Id,
            Result = new
            {
                protocolVersion = "2024-11-05",
                capabilities = new
                {
                    tools = new { }
                },
                serverInfo = new
                {
                    name = "Playwright MCP Server",
                    version = "1.0.0"
                }
            }
        };
    }

    private async Task<McpResponse> HandleListToolsAsync(McpRequest request)
    {
        var tools = await _toolRegistry.GetToolsAsync();
        
        return new McpResponse
        {
            Id = request.Id,
            Result = new ListToolsResponse
            {
                Tools = tools.Select(tool => new McpTool
                {
                    Name = tool.Name,
                    Description = tool.Description,
                    InputSchema = tool.InputSchema,
                    Annotations = new McpToolAnnotations
                    {
                        Title = tool.Title,
                        ReadOnlyHint = tool.IsReadOnly,
                        DestructiveHint = !tool.IsReadOnly,
                        OpenWorldHint = true
                    }
                }).ToArray()
            }
        };
    }

    private async Task<McpResponse> HandleCallToolAsync(McpRequest request)
    {
        if (request.Params == null)
        {
            return new McpResponse
            {
                Id = request.Id,
                Error = new McpError
                {
                    Code = -32602,
                    Message = "Invalid params"
                }
            };
        }

        var paramsJson = JsonSerializer.Serialize(request.Params);
        var callParams = JsonSerializer.Deserialize<CallToolParams>(paramsJson, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        if (callParams == null || string.IsNullOrEmpty(callParams.Name))
        {
            return new McpResponse
            {
                Id = request.Id,
                Error = new McpError
                {
                    Code = -32602,
                    Message = "Invalid tool call parameters"
                }
            };
        }

        try
        {
            var result = await _toolRegistry.ExecuteToolAsync(callParams.Name, callParams.Arguments ?? new Dictionary<string, object>());
            
            return new McpResponse
            {
                Id = request.Id,
                Result = result
            };
        }
        catch (ArgumentException ex)
        {
            return new McpResponse
            {
                Id = request.Id,
                Error = new McpError
                {
                    Code = -32602,
                    Message = ex.Message
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing tool {ToolName}", callParams.Name);
            
            return new McpResponse
            {
                Id = request.Id,
                Result = new McpToolResult
                {
                    Content = new McpContent[]
                    {
                        new McpTextContent { Text = $"Error: {ex.Message}" }
                    },
                    IsError = true
                }
            };
        }
    }

    public async ValueTask DisposeAsync()
    {
        await _browserManager.DisposeAsync();
        GC.SuppressFinalize(this);
    }
}
