// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.CommandLine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PlaywrightMcp.Configuration;
using PlaywrightMcp.Server;

namespace PlaywrightMcp;

/// <summary>
/// Main entry point for the Playwright MCP server.
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        var rootCommand = CommandLineBuilder.CreateRootCommand();
        
        rootCommand.SetHandler(async (context) =>
        {
            var config = await ConfigurationResolver.ResolveAsync(context.ParseResult);
            
            var host = Host.CreateDefaultBuilder()
                .ConfigureServices(services =>
                {
                    services.AddSingleton(config);
                    services.AddSingleton<McpServer>();
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });
                })
                .Build();

            var server = host.Services.GetRequiredService<McpServer>();
            await server.StartAsync(context.GetCancellationToken());
        });

        return await rootCommand.InvokeAsync(args);
    }
}
