// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.CommandLine.Parsing;
using PlaywrightMcp.Configuration;
using Xunit;

namespace PlaywrightMcp.Tests;

public class ConfigurationTests
{
    [Fact]
    public void CommandLineBuilder_CreatesRootCommand()
    {
        // Arrange & Act
        var rootCommand = CommandLineBuilder.CreateRootCommand();

        // Assert
        Assert.NotNull(rootCommand);
        Assert.Contains(rootCommand.Options, o => o.Name == "browser");
        Assert.Contains(rootCommand.Options, o => o.Name == "headless");
        Assert.Contains(rootCommand.Options, o => o.Name == "port");
    }

    [Fact]
    public async Task ConfigurationResolver_AppliesDefaults()
    {
        // Arrange
        var rootCommand = CommandLineBuilder.CreateRootCommand();
        var parseResult = rootCommand.Parse(Array.Empty<string>());

        // Act
        var config = await ConfigurationResolver.ResolveAsync(parseResult);

        // Assert
        Assert.NotNull(config);
        Assert.Equal("chromium", config.Browser.BrowserName);
        Assert.Equal("localhost", config.Server.Host);
        Assert.Equal("auto", config.ImageResponses);
        Assert.NotNull(config.Capabilities);
        Assert.Contains(ToolCapability.Core, config.Capabilities);
    }

    [Fact]
    public async Task ConfigurationResolver_AppliesCommandLineArguments()
    {
        // Arrange
        var rootCommand = CommandLineBuilder.CreateRootCommand();
        var parseResult = rootCommand.Parse(new[] { "--browser", "firefox", "--headless", "--port", "8080" });

        // Act
        var config = await ConfigurationResolver.ResolveAsync(parseResult);

        // Assert
        Assert.Equal("firefox", config.Browser.BrowserName);
        Assert.True(config.Browser.LaunchOptions?.Headless);
        Assert.Equal(8080, config.Server.Port);
    }

    [Fact]
    public async Task ConfigurationResolver_ValidatesConfig()
    {
        // Arrange
        var rootCommand = CommandLineBuilder.CreateRootCommand();
        var parseResult = rootCommand.Parse(new[] { "--browser", "invalid-browser" });

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => ConfigurationResolver.ResolveAsync(parseResult));
    }

    [Fact]
    public async Task ConfigurationResolver_ValidatesPortRange()
    {
        // Arrange
        var rootCommand = CommandLineBuilder.CreateRootCommand();
        var parseResult = rootCommand.Parse(new[] { "--port", "99999" });

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => ConfigurationResolver.ResolveAsync(parseResult));
    }
}
