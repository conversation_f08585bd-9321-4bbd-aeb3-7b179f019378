// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using PlaywrightMcp.Configuration;

namespace PlaywrightMcp.Tools;

/// <summary>
/// Manages browser instances and contexts for the MCP server.
/// </summary>
public class BrowserManager : IAsyncDisposable
{
    private readonly McpConfig _config;
    private readonly ILogger _logger;
    private IPlaywright? _playwright;
    private IBrowser? _browser;
    private IBrowserContext? _context;
    private readonly List<TabInfo> _tabs = new();
    private TabInfo? _currentTab;

    public BrowserManager(McpConfig config, ILogger logger)
    {
        _config = config;
        _logger = logger;
    }

    /// <summary>
    /// Gets the current tab or throws if none exists.
    /// </summary>
    public TabInfo CurrentTab => _currentTab ?? throw new InvalidOperationException("No current tab available. Navigate to a page first.");

    /// <summary>
    /// Gets all open tabs.
    /// </summary>
    public IReadOnlyList<TabInfo> Tabs => _tabs.AsReadOnly();

    /// <summary>
    /// Ensures a browser context is available and returns the current tab.
    /// </summary>
    public async Task<TabInfo> EnsureTabAsync()
    {
        await EnsureBrowserContextAsync();
        
        if (_currentTab == null)
        {
            await NewTabAsync();
        }

        return _currentTab!;
    }

    /// <summary>
    /// Creates a new tab and makes it current.
    /// </summary>
    public async Task<TabInfo> NewTabAsync(string? url = null)
    {
        await EnsureBrowserContextAsync();
        
        var page = await _context!.NewPageAsync();
        var tab = new TabInfo(page, _tabs.Count);
        
        _tabs.Add(tab);
        _currentTab = tab;

        if (!string.IsNullOrEmpty(url))
        {
            await page.GotoAsync(url);
        }

        _logger.LogInformation("Created new tab {TabIndex}", tab.Index);
        return tab;
    }

    /// <summary>
    /// Selects a tab by index.
    /// </summary>
    public async Task SelectTabAsync(int index)
    {
        if (index < 0 || index >= _tabs.Count)
        {
            throw new ArgumentOutOfRangeException(nameof(index), $"Tab index {index} is out of range. Available tabs: 0-{_tabs.Count - 1}");
        }

        _currentTab = _tabs[index];
        await _currentTab.Page.BringToFrontAsync();
        _logger.LogInformation("Selected tab {TabIndex}", index);
    }

    /// <summary>
    /// Closes a tab by index.
    /// </summary>
    public async Task CloseTabAsync(int? index = null)
    {
        var tabToClose = index.HasValue ? _tabs[index.Value] : _currentTab;
        if (tabToClose == null)
        {
            throw new InvalidOperationException("No tab to close");
        }

        await tabToClose.Page.CloseAsync();
        _tabs.Remove(tabToClose);

        // Update indices
        for (int i = 0; i < _tabs.Count; i++)
        {
            _tabs[i].Index = i;
        }

        // Update current tab
        if (_currentTab == tabToClose)
        {
            _currentTab = _tabs.LastOrDefault();
        }

        _logger.LogInformation("Closed tab {TabIndex}", tabToClose.Index);
    }

    /// <summary>
    /// Gets a markdown representation of all open tabs.
    /// </summary>
    public async Task<string> GetTabsMarkdownAsync()
    {
        if (_tabs.Count == 0)
        {
            return "### No tabs open";
        }

        var lines = new List<string> { "### Open tabs" };
        
        for (int i = 0; i < _tabs.Count; i++)
        {
            var tab = _tabs[i];
            var title = await tab.GetTitleAsync();
            var url = tab.Page.Url;
            var current = tab == _currentTab ? " (current)" : "";
            lines.Add($"- {i + 1}:{current} [{title}] ({url})");
        }

        return string.Join("\n", lines);
    }

    /// <summary>
    /// Resizes the browser window.
    /// </summary>
    public async Task ResizeAsync(int width, int height)
    {
        if (_currentTab == null)
        {
            throw new InvalidOperationException("No current tab available");
        }

        await _currentTab.Page.SetViewportSizeAsync(width, height);
        _logger.LogInformation("Resized browser window to {Width}x{Height}", width, height);
    }

    /// <summary>
    /// Closes the browser and all tabs.
    /// </summary>
    public async Task CloseAsync()
    {
        if (_browser != null)
        {
            await _browser.CloseAsync();
            _browser = null;
        }

        if (_playwright != null)
        {
            _playwright.Dispose();
            _playwright = null;
        }

        _context = null;
        _tabs.Clear();
        _currentTab = null;
        
        _logger.LogInformation("Browser closed");
    }

    private async Task EnsureBrowserContextAsync()
    {
        if (_context != null) return;

        _playwright = await Playwright.CreateAsync();
        
        var browserType = _config.Browser.BrowserName?.ToLowerInvariant() switch
        {
            "firefox" => _playwright.Firefox,
            "webkit" => _playwright.Webkit,
            "chrome" or "msedge" => _playwright.Chromium,
            _ => _playwright.Chromium
        };

        var launchOptions = CreateLaunchOptions();
        _browser = await browserType.LaunchAsync(launchOptions);

        var contextOptions = CreateContextOptions();
        _context = await _browser.NewContextAsync(contextOptions);

        // Set up request interception if needed
        await SetupRequestInterceptionAsync();

        _logger.LogInformation("Browser context created with {BrowserName}", _config.Browser.BrowserName);
    }

    private BrowserTypeLaunchOptions CreateLaunchOptions()
    {
        var options = new BrowserTypeLaunchOptions
        {
            Headless = _config.Browser.LaunchOptions?.Headless ?? false,
            Channel = _config.Browser.LaunchOptions?.Channel,
            ExecutablePath = _config.Browser.LaunchOptions?.ExecutablePath
        };

        if (_config.Browser.LaunchOptions?.Args != null)
        {
            options.Args = _config.Browser.LaunchOptions.Args;
        }

        return options;
    }

    private BrowserNewContextOptions CreateContextOptions()
    {
        var options = new BrowserNewContextOptions();

        if (_config.Browser.ContextOptions?.Viewport != null)
        {
            options.ViewportSize = new ViewportSize
            {
                Width = _config.Browser.ContextOptions.Viewport.Width,
                Height = _config.Browser.ContextOptions.Viewport.Height
            };
        }

        if (!string.IsNullOrEmpty(_config.Browser.ContextOptions?.UserAgent))
        {
            options.UserAgent = _config.Browser.ContextOptions.UserAgent;
        }

        if (!string.IsNullOrEmpty(_config.Browser.ContextOptions?.StorageState))
        {
            options.StorageStatePath = _config.Browser.ContextOptions.StorageState;
        }

        if (_config.Browser.ContextOptions?.Proxy != null)
        {
            options.Proxy = new Proxy
            {
                Server = _config.Browser.ContextOptions.Proxy.Server,
                Bypass = _config.Browser.ContextOptions.Proxy.Bypass
            };
        }

        return options;
    }

    private async Task SetupRequestInterceptionAsync()
    {
        if (_context == null) return;

        // Block origins if specified
        if (_config.Network?.BlockedOrigins?.Length > 0)
        {
            foreach (var origin in _config.Network.BlockedOrigins)
            {
                await _context.RouteAsync($"*://{origin}/**", route => route.AbortAsync());
            }
        }

        // Allow only specific origins if specified
        if (_config.Network?.AllowedOrigins?.Length > 0)
        {
            await _context.RouteAsync("**", route => route.AbortAsync());
            foreach (var origin in _config.Network.AllowedOrigins)
            {
                await _context.RouteAsync($"*://{origin}/**", route => route.ContinueAsync());
            }
        }
    }

    public async ValueTask DisposeAsync()
    {
        await CloseAsync();
        GC.SuppressFinalize(this);
    }
}
