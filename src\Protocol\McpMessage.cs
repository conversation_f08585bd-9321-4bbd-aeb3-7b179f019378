// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.Text.Json.Serialization;

namespace PlaywrightMcp.Protocol;

/// <summary>
/// Base class for all MCP messages.
/// </summary>
public abstract class McpMessage
{
    /// <summary>
    /// JSON-RPC version.
    /// </summary>
    [JsonPropertyName("jsonrpc")]
    public string JsonRpc { get; set; } = "2.0";
}

/// <summary>
/// MCP request message.
/// </summary>
public class McpRequest : McpMessage
{
    /// <summary>
    /// Request ID.
    /// </summary>
    [JsonPropertyName("id")]
    public object? Id { get; set; }

    /// <summary>
    /// Method name.
    /// </summary>
    [JsonPropertyName("method")]
    public string Method { get; set; } = string.Empty;

    /// <summary>
    /// Request parameters.
    /// </summary>
    [JsonPropertyName("params")]
    public object? Params { get; set; }
}

/// <summary>
/// MCP response message.
/// </summary>
public class McpResponse : McpMessage
{
    /// <summary>
    /// Request ID this response corresponds to.
    /// </summary>
    [JsonPropertyName("id")]
    public object? Id { get; set; }

    /// <summary>
    /// Response result.
    /// </summary>
    [JsonPropertyName("result")]
    public object? Result { get; set; }

    /// <summary>
    /// Error information if the request failed.
    /// </summary>
    [JsonPropertyName("error")]
    public McpError? Error { get; set; }
}

/// <summary>
/// MCP notification message.
/// </summary>
public class McpNotification : McpMessage
{
    /// <summary>
    /// Method name.
    /// </summary>
    [JsonPropertyName("method")]
    public string Method { get; set; } = string.Empty;

    /// <summary>
    /// Notification parameters.
    /// </summary>
    [JsonPropertyName("params")]
    public object? Params { get; set; }
}

/// <summary>
/// MCP error information.
/// </summary>
public class McpError
{
    /// <summary>
    /// Error code.
    /// </summary>
    [JsonPropertyName("code")]
    public int Code { get; set; }

    /// <summary>
    /// Error message.
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Additional error data.
    /// </summary>
    [JsonPropertyName("data")]
    public object? Data { get; set; }
}

/// <summary>
/// Tool definition for MCP.
/// </summary>
public class McpTool
{
    /// <summary>
    /// Tool name.
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Tool description.
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Input schema for the tool.
    /// </summary>
    [JsonPropertyName("inputSchema")]
    public object? InputSchema { get; set; }

    /// <summary>
    /// Tool annotations.
    /// </summary>
    [JsonPropertyName("annotations")]
    public McpToolAnnotations? Annotations { get; set; }
}

/// <summary>
/// Tool annotations for MCP.
/// </summary>
public class McpToolAnnotations
{
    /// <summary>
    /// Tool title.
    /// </summary>
    [JsonPropertyName("title")]
    public string? Title { get; set; }

    /// <summary>
    /// Whether the tool is read-only.
    /// </summary>
    [JsonPropertyName("readOnlyHint")]
    public bool? ReadOnlyHint { get; set; }

    /// <summary>
    /// Whether the tool is destructive.
    /// </summary>
    [JsonPropertyName("destructiveHint")]
    public bool? DestructiveHint { get; set; }

    /// <summary>
    /// Whether the tool supports open world.
    /// </summary>
    [JsonPropertyName("openWorldHint")]
    public bool? OpenWorldHint { get; set; }
}

/// <summary>
/// Content item for MCP responses.
/// </summary>
public abstract class McpContent
{
    /// <summary>
    /// Content type.
    /// </summary>
    [JsonPropertyName("type")]
    public abstract string Type { get; }
}

/// <summary>
/// Text content for MCP responses.
/// </summary>
public class McpTextContent : McpContent
{
    /// <summary>
    /// Content type.
    /// </summary>
    public override string Type => "text";

    /// <summary>
    /// Text content.
    /// </summary>
    [JsonPropertyName("text")]
    public string Text { get; set; } = string.Empty;
}

/// <summary>
/// Image content for MCP responses.
/// </summary>
public class McpImageContent : McpContent
{
    /// <summary>
    /// Content type.
    /// </summary>
    public override string Type => "image";

    /// <summary>
    /// Image data (base64 encoded).
    /// </summary>
    [JsonPropertyName("data")]
    public string Data { get; set; } = string.Empty;

    /// <summary>
    /// MIME type of the image.
    /// </summary>
    [JsonPropertyName("mimeType")]
    public string MimeType { get; set; } = string.Empty;
}

/// <summary>
/// Tool call result for MCP.
/// </summary>
public class McpToolResult
{
    /// <summary>
    /// Content items in the result.
    /// </summary>
    [JsonPropertyName("content")]
    public McpContent[]? Content { get; set; }

    /// <summary>
    /// Whether the tool call resulted in an error.
    /// </summary>
    [JsonPropertyName("isError")]
    public bool? IsError { get; set; }
}

/// <summary>
/// List tools request parameters.
/// </summary>
public class ListToolsParams
{
    // No parameters for list tools request
}

/// <summary>
/// List tools response.
/// </summary>
public class ListToolsResponse
{
    /// <summary>
    /// Available tools.
    /// </summary>
    [JsonPropertyName("tools")]
    public McpTool[] Tools { get; set; } = Array.Empty<McpTool>();
}

/// <summary>
/// Call tool request parameters.
/// </summary>
public class CallToolParams
{
    /// <summary>
    /// Name of the tool to call.
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Arguments to pass to the tool.
    /// </summary>
    [JsonPropertyName("arguments")]
    public Dictionary<string, object>? Arguments { get; set; }
}
