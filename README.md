# Playwright MCP Server for .NET

A Model Context Protocol (MCP) server that provides browser automation capabilities using [Microsoft Playwright](https://playwright.dev) for .NET 9. This server enables LLMs to interact with web pages through structured accessibility snapshots, bypassing the need for screenshots or visually-tuned models.

## Features

- **Fast and lightweight**: Uses <PERSON><PERSON>'s accessibility tree, not pixel-based input
- **LLM-friendly**: No vision models needed, operates purely on structured data  
- **Deterministic tool application**: Avoids ambiguity common with screenshot-based approaches
- **Cross-platform**: Runs on Windows, macOS, and Linux with .NET 9
- **WebSocket and STDIO support**: Flexible transport options for different integration scenarios

## Requirements

- .NET 9.0 or newer
- VS Code, Cursor, Windsurf, Claude <PERSON> or any other MCP client

## Installation

### From Source

1. Clone the repository:
```bash
git clone https://github.com/microsoft/playwright-mcp-dotnet.git
cd playwright-mcp-dotnet
```

2. Build the project:
```bash
dotnet build
```

3. Install Playwright browsers:
```bash
dotnet run -- --browser-install
```

## Configuration

### MCP Client Configuration

Add the following to your MCP client configuration:

```json
{
  "mcpServers": {
    "playwright": {
      "command": "dotnet",
      "args": [
        "run",
        "--project",
        "/path/to/playwright-mcp-dotnet"
      ]
    }
  }
}
```

### Command Line Options

The server supports the following command line options:

- `--allowed-origins <origins>`: Semicolon-separated list of origins to allow
- `--blocked-origins <origins>`: Semicolon-separated list of origins to block  
- `--browser <browser>`: Browser to use (chromium, firefox, webkit, chrome, msedge)
- `--headless`: Run browser in headless mode
- `--isolated`: Keep browser profile in memory only
- `--port <port>`: Port to listen on for WebSocket transport
- `--host <host>`: Host to bind server to (default: localhost)
- `--config <path>`: Path to JSON configuration file
- `--vision`: Enable vision mode (screenshots instead of accessibility snapshots)
- `--output-dir <path>`: Directory for output files
- `--user-data-dir <path>`: Path to user data directory
- `--viewport-size <size>`: Browser viewport size (e.g., "1280,720")

### Configuration File

You can use a JSON configuration file for more complex setups. See `config.sample.json` for an example.

```bash
dotnet run -- --config config.json
```

## Available Tools

### Core Tools
- `browser_snapshot`: Capture accessibility snapshot of the current page
- `browser_close`: Close the browser
- `browser_resize`: Resize browser window

### Interaction Tools  
- `browser_click`: Click on an element
- `browser_type`: Type text into an element
- `browser_drag`: Drag between elements (planned)

### Navigation Tools
- `browser_navigate`: Navigate to a URL
- `browser_navigate_back`: Go back in history (planned)
- `browser_navigate_forward`: Go forward in history (planned)

### Tab Management Tools
- `browser_tab_list`: List all open tabs
- `browser_tab_new`: Open a new tab
- `browser_tab_select`: Select a tab by index (planned)
- `browser_tab_close`: Close a tab (planned)

### Resource Tools (Planned)
- `browser_file_upload`: Upload files
- `browser_handle_dialog`: Handle dialogs
- `browser_take_screenshot`: Take screenshots
- `browser_pdf_save`: Save page as PDF

### Utility Tools (Planned)
- `browser_install`: Install browser
- `browser_wait_for`: Wait for conditions

## Usage Examples

### Basic Navigation and Interaction

```bash
# Start the server
dotnet run

# The server will accept MCP requests via STDIO
# Example requests would come from your MCP client
```

### WebSocket Mode

```bash
# Start server on port 8080
dotnet run -- --port 8080

# Connect via WebSocket at ws://localhost:8080
```

### Headless Mode

```bash
# Run in headless mode for server environments
dotnet run -- --headless
```

## Development

### Building

```bash
dotnet build
```

### Running Tests

```bash
dotnet test
```

### Project Structure

```
src/
├── Configuration/          # Configuration models and parsing
├── Protocol/              # MCP protocol message types
├── Server/                # WebSocket and STDIO server implementation
├── Browser/               # Browser management and tab handling
├── Tools/                 # Individual tool implementations
│   ├── Core/             # Core browser tools
│   ├── Interaction/      # User interaction tools
│   ├── Navigation/       # Navigation tools
│   ├── Resource/         # Resource management tools
│   ├── Tab/              # Tab management tools
│   └── Utility/          # Utility tools
└── Program.cs            # Main entry point
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Based on the original [Playwright MCP](https://github.com/microsoft/playwright-mcp) Node.js implementation
- Built with [Microsoft Playwright for .NET](https://playwright.dev/dotnet/)
- Implements the [Model Context Protocol](https://modelcontextprotocol.io/)
