// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.Net;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Server;

/// <summary>
/// Main MCP server implementation.
/// </summary>
public class McpServer
{
    private readonly McpConfig _config;
    private readonly ILogger<McpServer> _logger;
    private readonly McpRequestHandler _requestHandler;
    private HttpListener? _httpListener;
    private CancellationTokenSource? _cancellationTokenSource;

    public McpServer(McpConfig config, ILogger<McpServer> logger)
    {
        _config = config;
        _logger = logger;
        _requestHandler = new McpRequestHandler(config, logger);
    }

    /// <summary>
    /// Starts the MCP server.
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

        if (_config.Server.Port.HasValue)
        {
            await StartHttpServerAsync(_cancellationTokenSource.Token);
        }
        else
        {
            await StartStdioServerAsync(_cancellationTokenSource.Token);
        }
    }

    /// <summary>
    /// Stops the MCP server.
    /// </summary>
    public async Task StopAsync()
    {
        _cancellationTokenSource?.Cancel();
        
        if (_httpListener != null)
        {
            _httpListener.Stop();
            _httpListener.Close();
        }

        await _requestHandler.DisposeAsync();
    }

    private async Task StartHttpServerAsync(CancellationToken cancellationToken)
    {
        _httpListener = new HttpListener();
        var prefix = $"http://{_config.Server.Host}:{_config.Server.Port}/";
        _httpListener.Prefixes.Add(prefix);
        
        _logger.LogInformation("Starting HTTP server on {Prefix}", prefix);
        _httpListener.Start();

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var context = await _httpListener.GetContextAsync();
                _ = Task.Run(() => HandleHttpRequestAsync(context, cancellationToken), cancellationToken);
            }
            catch (HttpListenerException) when (cancellationToken.IsCancellationRequested)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting HTTP connection");
            }
        }
    }

    private async Task HandleHttpRequestAsync(HttpListenerContext context, CancellationToken cancellationToken)
    {
        try
        {
            if (context.Request.IsWebSocketRequest)
            {
                await HandleWebSocketRequestAsync(context, cancellationToken);
            }
            else
            {
                // Handle SSE or regular HTTP requests
                context.Response.StatusCode = 404;
                context.Response.Close();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling HTTP request");
            try
            {
                context.Response.StatusCode = 500;
                context.Response.Close();
            }
            catch
            {
                // Ignore errors when closing response
            }
        }
    }

    private async Task HandleWebSocketRequestAsync(HttpListenerContext context, CancellationToken cancellationToken)
    {
        var webSocketContext = await context.AcceptWebSocketAsync(null);
        var webSocket = webSocketContext.WebSocket;

        _logger.LogInformation("WebSocket connection established");

        try
        {
            await HandleWebSocketConnectionAsync(webSocket, cancellationToken);
        }
        finally
        {
            if (webSocket.State == WebSocketState.Open)
            {
                await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Server shutdown", cancellationToken);
            }
            _logger.LogInformation("WebSocket connection closed");
        }
    }

    private async Task HandleWebSocketConnectionAsync(WebSocket webSocket, CancellationToken cancellationToken)
    {
        var buffer = new byte[4096];
        var messageBuffer = new List<byte>();

        while (webSocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested)
        {
            try
            {
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);

                if (result.MessageType == WebSocketMessageType.Close)
                {
                    break;
                }

                messageBuffer.AddRange(buffer.Take(result.Count));

                if (result.EndOfMessage)
                {
                    var messageJson = Encoding.UTF8.GetString(messageBuffer.ToArray());
                    messageBuffer.Clear();

                    var response = await ProcessMessageAsync(messageJson);
                    if (response != null)
                    {
                        var responseJson = JsonSerializer.Serialize(response, new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                        });
                        var responseBytes = Encoding.UTF8.GetBytes(responseJson);
                        await webSocket.SendAsync(new ArraySegment<byte>(responseBytes), WebSocketMessageType.Text, true, cancellationToken);
                    }
                }
            }
            catch (WebSocketException) when (cancellationToken.IsCancellationRequested)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing WebSocket message");
                break;
            }
        }
    }

    private async Task StartStdioServerAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting STDIO server");

        var stdin = Console.OpenStandardInput();
        var stdout = Console.OpenStandardOutput();

        using var reader = new StreamReader(stdin);
        using var writer = new StreamWriter(stdout) { AutoFlush = true };

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var line = await reader.ReadLineAsync();
                if (line == null) break;

                var response = await ProcessMessageAsync(line);
                if (response != null)
                {
                    var responseJson = JsonSerializer.Serialize(response, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });
                    await writer.WriteLineAsync(responseJson);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing STDIO message");
            }
        }
    }

    private async Task<McpMessage?> ProcessMessageAsync(string messageJson)
    {
        try
        {
            var message = JsonSerializer.Deserialize<McpRequest>(messageJson, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (message == null)
            {
                return new McpResponse
                {
                    Id = null,
                    Error = new McpError
                    {
                        Code = -32700,
                        Message = "Parse error"
                    }
                };
            }

            return await _requestHandler.HandleRequestAsync(message);
        }
        catch (JsonException)
        {
            return new McpResponse
            {
                Id = null,
                Error = new McpError
                {
                    Code = -32700,
                    Message = "Parse error"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message");
            return new McpResponse
            {
                Id = null,
                Error = new McpError
                {
                    Code = -32603,
                    Message = "Internal error"
                }
            };
        }
    }
}
