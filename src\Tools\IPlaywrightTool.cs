// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using Microsoft.Extensions.Logging;
using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Tools;

/// <summary>
/// Represents a tool definition for MCP.
/// </summary>
public class ToolDefinition
{
    /// <summary>
    /// Tool name.
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Tool title.
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Tool description.
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Input schema for the tool.
    /// </summary>
    public object? InputSchema { get; set; }

    /// <summary>
    /// Whether the tool is read-only.
    /// </summary>
    public bool IsReadOnly { get; set; }

    /// <summary>
    /// Tool capability category.
    /// </summary>
    public ToolCapability Capability { get; set; }
}

/// <summary>
/// Context for tool execution.
/// </summary>
public class ToolExecutionContext
{
    /// <summary>
    /// Browser manager instance.
    /// </summary>
    public BrowserManager BrowserManager { get; }

    /// <summary>
    /// Configuration instance.
    /// </summary>
    public McpConfig Config { get; }

    /// <summary>
    /// Logger instance.
    /// </summary>
    public ILogger Logger { get; }

    public ToolExecutionContext(BrowserManager browserManager, McpConfig config, ILogger logger)
    {
        BrowserManager = browserManager;
        Config = config;
        Logger = logger;
    }
}

/// <summary>
/// Interface for all Playwright MCP tools.
/// </summary>
public interface IPlaywrightTool
{
    /// <summary>
    /// Tool definition.
    /// </summary>
    ToolDefinition Definition { get; }

    /// <summary>
    /// Tool capability category.
    /// </summary>
    ToolCapability Capability { get; }

    /// <summary>
    /// Executes the tool with the given parameters.
    /// </summary>
    Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters);
}

/// <summary>
/// Base class for Playwright MCP tools.
/// </summary>
public abstract class PlaywrightToolBase : IPlaywrightTool
{
    /// <summary>
    /// Tool definition.
    /// </summary>
    public abstract ToolDefinition Definition { get; }

    /// <summary>
    /// Tool capability category.
    /// </summary>
    public abstract ToolCapability Capability { get; }

    /// <summary>
    /// Executes the tool with the given parameters.
    /// </summary>
    public abstract Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters);

    /// <summary>
    /// Creates a successful result with text content.
    /// </summary>
    protected McpToolResult CreateTextResult(string text)
    {
        return new McpToolResult
        {
            Content = new McpContent[]
            {
                new McpTextContent { Text = text }
            }
        };
    }

    /// <summary>
    /// Creates a successful result with multiple content items.
    /// </summary>
    protected McpToolResult CreateResult(params McpContent[] content)
    {
        return new McpToolResult
        {
            Content = content
        };
    }

    /// <summary>
    /// Creates an error result.
    /// </summary>
    protected McpToolResult CreateErrorResult(string message)
    {
        return new McpToolResult
        {
            Content = new McpContent[]
            {
                new McpTextContent { Text = $"Error: {message}" }
            },
            IsError = true
        };
    }

    /// <summary>
    /// Gets a parameter value from the parameters dictionary.
    /// </summary>
    protected T? GetParameter<T>(Dictionary<string, object> parameters, string name, T? defaultValue = default)
    {
        if (!parameters.TryGetValue(name, out var value))
        {
            return defaultValue;
        }

        try
        {
            if (value is T directValue)
            {
                return directValue;
            }

            // Handle JSON element conversion
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                return jsonElement.Deserialize<T>();
            }

            // Try to convert
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Gets a required parameter value from the parameters dictionary.
    /// </summary>
    protected T GetRequiredParameter<T>(Dictionary<string, object> parameters, string name)
    {
        var value = GetParameter<T>(parameters, name);
        if (value == null)
        {
            throw new ArgumentException($"Required parameter '{name}' is missing or null");
        }
        return value;
    }

    /// <summary>
    /// Creates a JSON schema for a simple object.
    /// </summary>
    protected object CreateJsonSchema(Dictionary<string, object> properties, string[]? required = null)
    {
        var schema = new Dictionary<string, object>
        {
            ["type"] = "object",
            ["properties"] = properties
        };

        if (required?.Length > 0)
        {
            schema["required"] = required;
        }

        return schema;
    }

    /// <summary>
    /// Creates a string property schema.
    /// </summary>
    protected Dictionary<string, object> StringProperty(string description)
    {
        return new Dictionary<string, object>
        {
            ["type"] = "string",
            ["description"] = description
        };
    }

    /// <summary>
    /// Creates a number property schema.
    /// </summary>
    protected Dictionary<string, object> NumberProperty(string description)
    {
        return new Dictionary<string, object>
        {
            ["type"] = "number",
            ["description"] = description
        };
    }

    /// <summary>
    /// Creates a boolean property schema.
    /// </summary>
    protected Dictionary<string, object> BooleanProperty(string description)
    {
        return new Dictionary<string, object>
        {
            ["type"] = "boolean",
            ["description"] = description
        };
    }

    /// <summary>
    /// Creates an array property schema.
    /// </summary>
    protected Dictionary<string, object> ArrayProperty(string description, Dictionary<string, object> items)
    {
        return new Dictionary<string, object>
        {
            ["type"] = "array",
            ["description"] = description,
            ["items"] = items
        };
    }
}
