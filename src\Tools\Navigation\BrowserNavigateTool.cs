// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Tools.Navigation;

/// <summary>
/// Tool for navigating to URLs.
/// </summary>
public class BrowserNavigateTool : PlaywrightToolBase
{
    public override ToolDefinition Definition => new()
    {
        Name = "browser_navigate",
        Title = "Navigate to a URL",
        Description = "Navigate to a URL",
        IsReadOnly = false,
        Capability = ToolCapability.Core,
        InputSchema = CreateJsonSchema(
            new Dictionary<string, object>
            {
                ["url"] = StringProperty("The URL to navigate to")
            },
            new[] { "url" })
    };

    public override ToolCapability Capability => ToolCapability.Core;

    public override async Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters)
    {
        var url = GetRequiredParameter<string>(parameters, "url");

        try
        {
            var tab = await context.BrowserManager.EnsureTabAsync();
            await tab.NavigateAsync(url);

            var result = new List<string>
            {
                "- Ran Playwright code: ```js",
                $"// Navigate to {url}",
                $"await page.goto('{url}');",
                "```"
            };

            // Capture snapshot after navigation
            var snapshot = await tab.CaptureSnapshotAsync();
            result.Add("");
            result.Add($"- Page URL: {tab.GetUrl()}");
            result.Add($"- Page Title: {await tab.GetTitleAsync()}");
            result.Add("");
            result.Add(snapshot);

            return CreateTextResult(string.Join("\n", result));
        }
        catch (Exception ex)
        {
            return CreateErrorResult($"Failed to navigate to '{url}': {ex.Message}");
        }
    }
}
