// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.CommandLine.Parsing;
using System.Text.Json;

namespace PlaywrightMcp.Configuration;

/// <summary>
/// Resolves configuration from command line arguments and configuration files.
/// </summary>
public static class ConfigurationResolver
{
    /// <summary>
    /// Resolves the final configuration from command line arguments and config files.
    /// </summary>
    public static async Task<McpConfig> ResolveAsync(ParseResult parseResult)
    {
        var config = new McpConfig();

        // Load from config file if specified
        var configPath = parseResult.GetValueForOption<string>("--config");
        if (!string.IsNullOrEmpty(configPath))
        {
            config = await LoadConfigFileAsync(configPath);
        }

        // Override with command line arguments
        ApplyCommandLineArguments(config, parseResult);

        // Apply defaults and validation
        ApplyDefaults(config);
        ValidateConfig(config);

        return config;
    }

    private static async Task<McpConfig> LoadConfigFileAsync(string configPath)
    {
        if (!File.Exists(configPath))
        {
            throw new FileNotFoundException($"Configuration file not found: {configPath}");
        }

        var json = await File.ReadAllTextAsync(configPath);
        var options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            AllowTrailingCommas = true,
            ReadCommentHandling = JsonCommentHandling.Skip
        };

        return JsonSerializer.Deserialize<McpConfig>(json, options) ?? new McpConfig();
    }

    private static void ApplyCommandLineArguments(McpConfig config, ParseResult parseResult)
    {
        // Browser options
        if (parseResult.GetValueForOption<string>("--browser") is string browser)
            config.Browser.BrowserName = browser;

        if (parseResult.GetValueForOption<bool>("--headless"))
        {
            config.Browser.LaunchOptions ??= new LaunchOptions();
            config.Browser.LaunchOptions.Headless = true;
        }

        if (parseResult.GetValueForOption<string>("--executable-path") is string execPath)
        {
            config.Browser.LaunchOptions ??= new LaunchOptions();
            config.Browser.LaunchOptions.ExecutablePath = execPath;
        }

        if (parseResult.GetValueForOption<bool>("--isolated"))
            config.Browser.Isolated = true;

        if (parseResult.GetValueForOption<string>("--user-data-dir") is string userDataDir)
            config.Browser.UserDataDir = userDataDir;

        if (parseResult.GetValueForOption<string>("--cdp-endpoint") is string cdpEndpoint)
            config.Browser.CdpEndpoint = cdpEndpoint;

        if (parseResult.GetValueForOption<string>("--device") is string device)
        {
            config.Browser.ContextOptions ??= new ContextOptions();
            config.Browser.ContextOptions.Device = device;
        }

        if (parseResult.GetValueForOption<string>("--user-agent") is string userAgent)
        {
            config.Browser.ContextOptions ??= new ContextOptions();
            config.Browser.ContextOptions.UserAgent = userAgent;
        }

        if (parseResult.GetValueForOption<string>("--storage-state") is string storageState)
        {
            config.Browser.ContextOptions ??= new ContextOptions();
            config.Browser.ContextOptions.StorageState = storageState;
        }

        if (parseResult.GetValueForOption<string>("--viewport-size") is string viewportSize)
        {
            var parts = viewportSize.Split(',');
            if (parts.Length == 2 && int.TryParse(parts[0].Trim(), out var width) && int.TryParse(parts[1].Trim(), out var height))
            {
                config.Browser.ContextOptions ??= new ContextOptions();
                config.Browser.ContextOptions.Viewport = new ViewportSize { Width = width, Height = height };
            }
        }

        // Proxy options
        if (parseResult.GetValueForOption<string>("--proxy-server") is string proxyServer)
        {
            config.Browser.ContextOptions ??= new ContextOptions();
            config.Browser.ContextOptions.Proxy ??= new ProxyConfig();
            config.Browser.ContextOptions.Proxy.Server = proxyServer;
        }

        if (parseResult.GetValueForOption<string>("--proxy-bypass") is string proxyBypass)
        {
            config.Browser.ContextOptions ??= new ContextOptions();
            config.Browser.ContextOptions.Proxy ??= new ProxyConfig();
            config.Browser.ContextOptions.Proxy.Bypass = proxyBypass;
        }

        // Launch options
        if (parseResult.GetValueForOption<bool>("--no-sandbox"))
        {
            config.Browser.LaunchOptions ??= new LaunchOptions();
            config.Browser.LaunchOptions.NoSandbox = true;
        }

        if (parseResult.GetValueForOption<bool>("--ignore-https-errors"))
        {
            config.Browser.LaunchOptions ??= new LaunchOptions();
            config.Browser.LaunchOptions.IgnoreHTTPSErrors = true;
        }

        // Server options
        if (parseResult.GetValueForOption<int>("--port") is int port)
            config.Server.Port = port;

        if (parseResult.GetValueForOption<string>("--host") is string host)
            config.Server.Host = host;

        // Capabilities
        if (parseResult.GetValueForOption<string[]>("--caps") is string[] caps)
        {
            config.Capabilities = caps.SelectMany(c => c.Split(','))
                .Select(c => Enum.Parse<ToolCapability>(c.Trim(), true))
                .ToArray();
        }

        // Network options
        if (parseResult.GetValueForOption<string[]>("--allowed-origins") is string[] allowedOrigins)
        {
            config.Network ??= new NetworkConfig();
            config.Network.AllowedOrigins = allowedOrigins.SelectMany(o => o.Split(';')).ToArray();
        }

        if (parseResult.GetValueForOption<string[]>("--blocked-origins") is string[] blockedOrigins)
        {
            config.Network ??= new NetworkConfig();
            config.Network.BlockedOrigins = blockedOrigins.SelectMany(o => o.Split(';')).ToArray();
        }

        // Other options
        if (parseResult.GetValueForOption<bool>("--vision"))
            config.Vision = true;

        if (parseResult.GetValueForOption<bool>("--save-trace"))
            config.SaveTrace = true;

        if (parseResult.GetValueForOption<string>("--output-dir") is string outputDir)
            config.OutputDir = outputDir;

        if (parseResult.GetValueForOption<string>("--image-responses") is string imageResponses)
            config.ImageResponses = imageResponses;
    }

    private static void ApplyDefaults(McpConfig config)
    {
        // Set default capabilities if none specified
        config.Capabilities ??= Enum.GetValues<ToolCapability>();

        // Set default output directory
        config.OutputDir ??= Path.Combine(Path.GetTempPath(), "playwright-mcp");

        // Ensure output directory exists
        if (!Directory.Exists(config.OutputDir))
        {
            Directory.CreateDirectory(config.OutputDir);
        }
    }

    private static void ValidateConfig(McpConfig config)
    {
        // Validate browser name
        var validBrowsers = new[] { "chromium", "firefox", "webkit", "chrome", "msedge" };
        if (!string.IsNullOrEmpty(config.Browser.BrowserName) && 
            !validBrowsers.Contains(config.Browser.BrowserName.ToLowerInvariant()))
        {
            throw new ArgumentException($"Invalid browser name: {config.Browser.BrowserName}. Valid options: {string.Join(", ", validBrowsers)}");
        }

        // Validate image responses mode
        var validImageModes = new[] { "allow", "omit", "auto" };
        if (!validImageModes.Contains(config.ImageResponses.ToLowerInvariant()))
        {
            throw new ArgumentException($"Invalid image responses mode: {config.ImageResponses}. Valid options: {string.Join(", ", validImageModes)}");
        }

        // Validate port range
        if (config.Server.Port.HasValue && (config.Server.Port < 1 || config.Server.Port > 65535))
        {
            throw new ArgumentException($"Invalid port number: {config.Server.Port}. Must be between 1 and 65535.");
        }
    }
}
