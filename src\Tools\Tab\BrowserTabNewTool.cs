// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Tools.Tab;

/// <summary>
/// Tool for creating new browser tabs.
/// </summary>
public class BrowserTabNewTool : PlaywrightToolBase
{
    public override ToolDefinition Definition => new()
    {
        Name = "browser_tab_new",
        Title = "Open a new tab",
        Description = "Open a new tab",
        IsReadOnly = true,
        Capability = ToolCapability.Tabs,
        InputSchema = CreateJsonSchema(
            new Dictionary<string, object>
            {
                ["url"] = StringProperty("The URL to navigate to in the new tab. If not provided, the new tab will be blank.")
            })
    };

    public override ToolCapability Capability => ToolCapability.Tabs;

    public override async Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters)
    {
        var url = GetParameter<string>(parameters, "url");

        try
        {
            var tab = await context.BrowserManager.NewTabAsync(url);

            var result = new List<string>
            {
                "- Ran Playwright code: ```js",
                "// Open a new tab",
                "const newPage = await context.newPage();"
            };

            if (!string.IsNullOrEmpty(url))
            {
                result.Add($"await newPage.goto('{url}');");
            }

            result.Add("```");

            // Add tabs list
            var tabsMarkdown = await context.BrowserManager.GetTabsMarkdownAsync();
            result.Add("");
            result.Add(tabsMarkdown);

            // Add current tab info if we navigated
            if (!string.IsNullOrEmpty(url))
            {
                result.Add("");
                result.Add("### Current tab");
                result.Add($"- Page URL: {tab.GetUrl()}");
                result.Add($"- Page Title: {await tab.GetTitleAsync()}");

                // Capture snapshot if we navigated
                var snapshot = await tab.CaptureSnapshotAsync();
                result.Add("");
                result.Add(snapshot);
            }

            return CreateTextResult(string.Join("\n", result));
        }
        catch (Exception ex)
        {
            return CreateErrorResult($"Failed to create new tab: {ex.Message}");
        }
    }
}
