// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Tools.Core;

/// <summary>
/// Tool for capturing accessibility snapshots.
/// </summary>
public class BrowserSnapshotTool : PlaywrightToolBase
{
    public override ToolDefinition Definition => new()
    {
        Name = "browser_snapshot",
        Title = "Page snapshot",
        Description = "Capture accessibility snapshot of the current page, this is better than screenshot",
        IsReadOnly = true,
        Capability = ToolCapability.Core,
        InputSchema = CreateJsonSchema(new Dictionary<string, object>())
    };

    public override ToolCapability Capability => ToolCapability.Core;

    public override async Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters)
    {
        var currentTab = context.BrowserManager.CurrentTab;
        var snapshot = await currentTab.CaptureSnapshotAsync();

        var result = new List<string>
        {
            "- Ran Playwright code: ```js",
            "await page.accessibility.snapshot()",
            "```",
            "",
            $"- Page URL: {currentTab.GetUrl()}",
            $"- Page Title: {await currentTab.GetTitleAsync()}",
            "",
            snapshot
        };

        return CreateTextResult(string.Join("\n", result));
    }
}
