// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.Text.Json.Serialization;

namespace PlaywrightMcp.Configuration;

/// <summary>
/// Tool capability types supported by the MCP server.
/// </summary>
public enum ToolCapability
{
    Core,
    Tabs,
    Pdf,
    History,
    Wait,
    Files,
    Install,
    Testing
}

/// <summary>
/// Browser configuration options.
/// </summary>
public class BrowserConfig
{
    /// <summary>
    /// Browser type to use (chromium, firefox, webkit).
    /// </summary>
    [JsonPropertyName("browserName")]
    public string? BrowserName { get; set; } = "chromium";

    /// <summary>
    /// Keep the browser profile in memory, do not save it to disk.
    /// </summary>
    [JsonPropertyName("isolated")]
    public bool Isolated { get; set; }

    /// <summary>
    /// Path to user data directory for browser profile persistence.
    /// </summary>
    [JsonPropertyName("userDataDir")]
    public string? UserDataDir { get; set; }

    /// <summary>
    /// Browser launch options.
    /// </summary>
    [JsonPropertyName("launchOptions")]
    public LaunchOptions? LaunchOptions { get; set; }

    /// <summary>
    /// Browser context options.
    /// </summary>
    [JsonPropertyName("contextOptions")]
    public ContextOptions? ContextOptions { get; set; }

    /// <summary>
    /// CDP endpoint for connecting to existing browser.
    /// </summary>
    [JsonPropertyName("cdpEndpoint")]
    public string? CdpEndpoint { get; set; }

    /// <summary>
    /// Remote Playwright server endpoint.
    /// </summary>
    [JsonPropertyName("remoteEndpoint")]
    public string? RemoteEndpoint { get; set; }
}

/// <summary>
/// Browser launch options.
/// </summary>
public class LaunchOptions
{
    /// <summary>
    /// Browser channel (e.g., 'chrome').
    /// </summary>
    [JsonPropertyName("channel")]
    public string? Channel { get; set; }

    /// <summary>
    /// Run in headless mode.
    /// </summary>
    [JsonPropertyName("headless")]
    public bool? Headless { get; set; }

    /// <summary>
    /// Path to browser executable.
    /// </summary>
    [JsonPropertyName("executablePath")]
    public string? ExecutablePath { get; set; }

    /// <summary>
    /// Additional arguments to pass to the browser.
    /// </summary>
    [JsonPropertyName("args")]
    public string[]? Args { get; set; }

    /// <summary>
    /// Disable sandbox for all process types.
    /// </summary>
    [JsonPropertyName("noSandbox")]
    public bool? NoSandbox { get; set; }

    /// <summary>
    /// Ignore HTTPS errors.
    /// </summary>
    [JsonPropertyName("ignoreHTTPSErrors")]
    public bool? IgnoreHTTPSErrors { get; set; }
}

/// <summary>
/// Browser context options.
/// </summary>
public class ContextOptions
{
    /// <summary>
    /// Viewport size.
    /// </summary>
    [JsonPropertyName("viewport")]
    public ViewportSize? Viewport { get; set; }

    /// <summary>
    /// User agent string.
    /// </summary>
    [JsonPropertyName("userAgent")]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Device to emulate.
    /// </summary>
    [JsonPropertyName("device")]
    public string? Device { get; set; }

    /// <summary>
    /// Storage state file path.
    /// </summary>
    [JsonPropertyName("storageState")]
    public string? StorageState { get; set; }

    /// <summary>
    /// Proxy configuration.
    /// </summary>
    [JsonPropertyName("proxy")]
    public ProxyConfig? Proxy { get; set; }
}

/// <summary>
/// Viewport size configuration.
/// </summary>
public class ViewportSize
{
    /// <summary>
    /// Viewport width.
    /// </summary>
    [JsonPropertyName("width")]
    public int Width { get; set; } = 1280;

    /// <summary>
    /// Viewport height.
    /// </summary>
    [JsonPropertyName("height")]
    public int Height { get; set; } = 720;
}

/// <summary>
/// Proxy configuration.
/// </summary>
public class ProxyConfig
{
    /// <summary>
    /// Proxy server URL.
    /// </summary>
    [JsonPropertyName("server")]
    public string? Server { get; set; }

    /// <summary>
    /// Domains to bypass proxy.
    /// </summary>
    [JsonPropertyName("bypass")]
    public string? Bypass { get; set; }
}

/// <summary>
/// Server configuration options.
/// </summary>
public class ServerConfig
{
    /// <summary>
    /// Port to listen on for SSE or MCP transport.
    /// </summary>
    [JsonPropertyName("port")]
    public int? Port { get; set; }

    /// <summary>
    /// Host to bind the server to.
    /// </summary>
    [JsonPropertyName("host")]
    public string Host { get; set; } = "localhost";
}

/// <summary>
/// Network configuration options.
/// </summary>
public class NetworkConfig
{
    /// <summary>
    /// List of origins to allow the browser to request.
    /// </summary>
    [JsonPropertyName("allowedOrigins")]
    public string[]? AllowedOrigins { get; set; }

    /// <summary>
    /// List of origins to block the browser from requesting.
    /// </summary>
    [JsonPropertyName("blockedOrigins")]
    public string[]? BlockedOrigins { get; set; }
}

/// <summary>
/// Main configuration class for the MCP server.
/// </summary>
public class McpConfig
{
    /// <summary>
    /// Browser configuration.
    /// </summary>
    [JsonPropertyName("browser")]
    public BrowserConfig Browser { get; set; } = new();

    /// <summary>
    /// Server configuration.
    /// </summary>
    [JsonPropertyName("server")]
    public ServerConfig Server { get; set; } = new();

    /// <summary>
    /// List of enabled tool capabilities.
    /// </summary>
    [JsonPropertyName("capabilities")]
    public ToolCapability[]? Capabilities { get; set; }

    /// <summary>
    /// Enable vision mode (screenshots instead of accessibility snapshots).
    /// </summary>
    [JsonPropertyName("vision")]
    public bool Vision { get; set; }

    /// <summary>
    /// Whether to save Playwright trace.
    /// </summary>
    [JsonPropertyName("saveTrace")]
    public bool SaveTrace { get; set; }

    /// <summary>
    /// Directory for output files.
    /// </summary>
    [JsonPropertyName("outputDir")]
    public string? OutputDir { get; set; }

    /// <summary>
    /// Network configuration.
    /// </summary>
    [JsonPropertyName("network")]
    public NetworkConfig? Network { get; set; }

    /// <summary>
    /// Image response mode.
    /// </summary>
    [JsonPropertyName("imageResponses")]
    public string ImageResponses { get; set; } = "auto";
}
