// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using Microsoft.Extensions.Logging;
using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;
using PlaywrightMcp.Tools.Core;
using PlaywrightMcp.Tools.Interaction;
using PlaywrightMcp.Tools.Navigation;
using PlaywrightMcp.Tools.Resource;
using PlaywrightMcp.Tools.Tab;
using PlaywrightMcp.Tools.Utility;

namespace PlaywrightMcp.Tools;

/// <summary>
/// Registry for all available MCP tools.
/// </summary>
public class ToolRegistry
{
    private readonly McpConfig _config;
    private readonly BrowserManager _browserManager;
    private readonly ILogger _logger;
    private readonly Dictionary<string, IPlaywrightTool> _tools = new();

    public ToolRegistry(McpConfig config, BrowserManager browserManager, ILogger logger)
    {
        _config = config;
        _browserManager = browserManager;
        _logger = logger;
        RegisterTools();
    }

    /// <summary>
    /// Gets all available tools based on configuration.
    /// </summary>
    public async Task<IEnumerable<ToolDefinition>> GetToolsAsync()
    {
        var enabledCapabilities = _config.Capabilities ?? Enum.GetValues<ToolCapability>();

        return _tools.Values
            .Where(tool => enabledCapabilities.Contains(tool.Capability))
            .Select(tool => tool.Definition);
    }

    /// <summary>
    /// Executes a tool with the given parameters.
    /// </summary>
    public async Task<McpToolResult> ExecuteToolAsync(string toolName, Dictionary<string, object> parameters)
    {
        if (!_tools.TryGetValue(toolName, out var tool))
        {
            throw new ArgumentException($"Tool '{toolName}' not found");
        }

        try
        {
            var context = new ToolExecutionContext(_browserManager, _config, _logger);
            return await tool.ExecuteAsync(context, parameters);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing tool {ToolName}", toolName);
            return new McpToolResult
            {
                Content = new McpContent[]
                {
                    new McpTextContent { Text = $"Error executing {toolName}: {ex.Message}" }
                },
                IsError = true
            };
        }
    }

    private void RegisterTools()
    {
        // Core tools
        RegisterTool(new BrowserCloseTool());
        RegisterTool(new BrowserResizeTool());
        RegisterTool(new BrowserSnapshotTool());

        // Interaction tools
        RegisterTool(new BrowserClickTool());
        RegisterTool(new BrowserTypeTool());
        RegisterTool(new BrowserDragTool());
        // RegisterTool(new BrowserHoverTool());
        // RegisterTool(new BrowserSelectOptionTool());
        // RegisterTool(new BrowserPressKeyTool());

        // Vision mode tools (if enabled)
        // if (_config.Vision)
        // {
        //     RegisterTool(new BrowserScreenCaptureTool());
        //     RegisterTool(new BrowserScreenClickTool());
        //     RegisterTool(new BrowserScreenDragTool());
        //     RegisterTool(new BrowserScreenTypeTool());
        //     RegisterTool(new BrowserScreenMoveMouseTool());
        // }

        // Navigation tools
        RegisterTool(new BrowserNavigateTool());
        // RegisterTool(new BrowserNavigateBackTool());
        // RegisterTool(new BrowserNavigateForwardTool());

        // Resource tools
        // RegisterTool(new BrowserFileUploadTool());
        // RegisterTool(new BrowserHandleDialogTool());
        // RegisterTool(new BrowserTakeScreenshotTool());
        // RegisterTool(new BrowserPdfSaveTool());
        // RegisterTool(new BrowserNetworkRequestsTool());
        // RegisterTool(new BrowserConsoleMessagesTool());

        // Tab management tools
        RegisterTool(new BrowserTabListTool());
        RegisterTool(new BrowserTabNewTool());
        // RegisterTool(new BrowserTabSelectTool());
        // RegisterTool(new BrowserTabCloseTool());

        // Utility tools
        // RegisterTool(new BrowserInstallTool());
        // RegisterTool(new BrowserWaitForTool());

        _logger.LogInformation("Registered {ToolCount} tools", _tools.Count);
    }

    private void RegisterTool(IPlaywrightTool tool)
    {
        _tools[tool.Definition.Name] = tool;
    }
}
