// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using PlaywrightMcp.Configuration;
using PlaywrightMcp.Protocol;

namespace PlaywrightMcp.Tools.Interaction;

/// <summary>
/// Tool for typing text into elements.
/// </summary>
public class BrowserTypeTool : PlaywrightToolBase
{
    public override ToolDefinition Definition => new()
    {
        Name = "browser_type",
        Title = "Type text",
        Description = "Type text into editable element",
        IsReadOnly = false,
        Capability = ToolCapability.Core,
        InputSchema = CreateJsonSchema(
            new Dictionary<string, object>
            {
                ["element"] = StringProperty("Human-readable element description used to obtain permission to interact with the element"),
                ["ref"] = StringProperty("Exact target element reference from the page snapshot"),
                ["text"] = StringProperty("Text to type into the element"),
                ["submit"] = BooleanProperty("Whether to submit entered text (press Enter after)"),
                ["slowly"] = BooleanProperty("Whether to type one character at a time. Useful for triggering key handlers in the page. By default entire text is filled in at once.")
            },
            new[] { "element", "ref", "text" })
    };

    public override ToolCapability Capability => ToolCapability.Core;

    public override async Task<McpToolResult> ExecuteAsync(ToolExecutionContext context, Dictionary<string, object> parameters)
    {
        var element = GetRequiredParameter<string>(parameters, "element");
        var elementRef = GetRequiredParameter<string>(parameters, "ref");
        var text = GetRequiredParameter<string>(parameters, "text");
        var submit = GetParameter<bool>(parameters, "submit", false);
        var slowly = GetParameter<bool>(parameters, "slowly", false);

        var currentTab = context.BrowserManager.CurrentTab;

        try
        {
            var locator = currentTab.Page.Locator(elementRef);

            if (slowly)
            {
                await locator.TypeAsync(text);
            }
            else
            {
                await locator.FillAsync(text);
            }

            if (submit)
            {
                await locator.PressAsync("Enter");
            }

            var result = new List<string>
            {
                "- Ran Playwright code: ```js",
                $"// Type text into {element}",
                slowly ? 
                    $"await page.locator('{elementRef}').type('{text}');" :
                    $"await page.locator('{elementRef}').fill('{text}');"
            };

            if (submit)
            {
                result.Add($"await page.locator('{elementRef}').press('Enter');");
            }

            result.Add("```");

            // Capture new snapshot after action
            var snapshot = await currentTab.CaptureSnapshotAsync();
            result.Add("");
            result.Add($"- Page URL: {currentTab.GetUrl()}");
            result.Add($"- Page Title: {await currentTab.GetTitleAsync()}");
            result.Add("");
            result.Add(snapshot);

            return CreateTextResult(string.Join("\n", result));
        }
        catch (Exception ex)
        {
            return CreateErrorResult($"Failed to type into element '{element}': {ex.Message}");
        }
    }
}
