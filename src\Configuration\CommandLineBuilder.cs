// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using System.CommandLine;

namespace PlaywrightMcp.Configuration;

/// <summary>
/// Builds the command line interface for the MCP server.
/// </summary>
public static class CommandLineBuilder
{
    /// <summary>
    /// Creates the root command with all supported options.
    /// </summary>
    public static RootCommand CreateRootCommand()
    {
        var rootCommand = new RootCommand("Playwright MCP Server for .NET")
        {
            // Origins and blocking
            new Option<string[]>("--allowed-origins", "Semicolon-separated list of origins to allow the browser to request. Default is to allow all.")
            {
                AllowMultipleArgumentsPerToken = true
            },
            new Option<string[]>("--blocked-origins", "Semicolon-separated list of origins to block the browser from requesting.")
            {
                AllowMultipleArgumentsPerToken = true
            },
            new Option<bool>("--block-service-workers", "Block service workers"),

            // Browser options
            new Option<string>("--browser", "Browser or chrome channel to use, possible values: chrome, firefox, webkit, msedge."),
            new Option<string>("--browser-agent", "Use browser agent (experimental)."),
            new Option<string[]>("--caps", "Comma-separated list of capabilities to enable, possible values: tabs, pdf, history, wait, files, install. Default is all.")
            {
                AllowMultipleArgumentsPerToken = true
            },
            new Option<string>("--cdp-endpoint", "CDP endpoint to connect to."),
            new Option<string>("--config", "Path to the configuration file."),
            new Option<string>("--device", "Device to emulate, for example: \"iPhone 15\""),
            new Option<string>("--executable-path", "Path to the browser executable."),
            new Option<bool>("--headless", "Run browser in headless mode, headed by default"),

            // Server options
            new Option<string>("--host", "Host to bind server to. Default is localhost. Use 0.0.0.0 to bind to all interfaces."),
            new Option<bool>("--ignore-https-errors", "Ignore https errors"),
            new Option<bool>("--isolated", "Keep the browser profile in memory, do not save it to disk."),
            new Option<string>("--image-responses", "Whether to send image responses to the client. Can be \"allow\", \"omit\", or \"auto\". Defaults to \"auto\"."),
            new Option<bool>("--no-sandbox", "Disable the sandbox for all process types that are normally sandboxed."),
            new Option<string>("--output-dir", "Path to the directory for output files."),
            new Option<int>("--port", "Port to listen on for SSE transport."),

            // Proxy options
            new Option<string>("--proxy-bypass", "Comma-separated domains to bypass proxy, for example \".com,chromium.org,.domain.com\""),
            new Option<string>("--proxy-server", "Specify proxy server, for example \"http://myproxy:3128\" or \"socks5://myproxy:8080\""),

            // Trace and storage
            new Option<bool>("--save-trace", "Whether to save the Playwright Trace of the session into the output directory."),
            new Option<string>("--storage-state", "Path to the storage state file for isolated sessions."),
            new Option<string>("--user-agent", "Specify user agent string"),
            new Option<string>("--user-data-dir", "Path to the user data directory. If not specified, a temporary directory will be created."),
            new Option<string>("--viewport-size", "Specify browser viewport size in pixels, for example \"1280, 720\""),
            new Option<bool>("--vision", "Run server that uses screenshots (Aria snapshots are used by default)")
        };

        return rootCommand;
    }
}
