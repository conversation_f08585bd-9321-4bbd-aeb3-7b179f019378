// Copyright (c) Microsoft Corporation.
// Licensed under the Apache License, Version 2.0.

using Microsoft.Playwright;

namespace PlaywrightMcp.Tools;

/// <summary>
/// Represents information about a browser tab.
/// </summary>
public class TabInfo
{
    /// <summary>
    /// The Playwright page instance.
    /// </summary>
    public IPage Page { get; }

    /// <summary>
    /// The tab index.
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// The last captured accessibility snapshot.
    /// </summary>
    public string? LastSnapshot { get; set; }

    /// <summary>
    /// Timestamp of the last snapshot.
    /// </summary>
    public DateTime? LastSnapshotTime { get; set; }

    public TabInfo(IPage page, int index)
    {
        Page = page;
        Index = index;
    }

    /// <summary>
    /// Gets the title of the page.
    /// </summary>
    public async Task<string> GetTitleAsync()
    {
        try
        {
            return await Page.TitleAsync();
        }
        catch
        {
            return "Unknown";
        }
    }

    /// <summary>
    /// Gets the URL of the page.
    /// </summary>
    public string GetUrl()
    {
        return Page.Url;
    }

    /// <summary>
    /// Captures an accessibility snapshot of the page.
    /// </summary>
    public async Task<string> CaptureSnapshotAsync()
    {
        try
        {
            // Use Playwright's accessibility snapshot feature
            var snapshot = await Page.Accessibility.SnapshotAsync();
            LastSnapshot = FormatAccessibilitySnapshot(snapshot);
            LastSnapshotTime = DateTime.UtcNow;
            return LastSnapshot;
        }
        catch (Exception)
        {
            // Fallback to basic page content
            var content = await Page.ContentAsync();
            LastSnapshot = $"Page content (fallback):\n{content}";
            LastSnapshotTime = DateTime.UtcNow;
            return LastSnapshot;
        }
    }

    /// <summary>
    /// Takes a screenshot of the page.
    /// </summary>
    public async Task<byte[]> TakeScreenshotAsync(bool fullPage = false)
    {
        return await Page.ScreenshotAsync(new PageScreenshotOptions
        {
            FullPage = fullPage,
            Type = ScreenshotType.Png
        });
    }

    /// <summary>
    /// Navigates to a URL.
    /// </summary>
    public async Task NavigateAsync(string url)
    {
        await Page.GotoAsync(url);
    }

    /// <summary>
    /// Goes back in browser history.
    /// </summary>
    public async Task GoBackAsync()
    {
        await Page.GoBackAsync();
    }

    /// <summary>
    /// Goes forward in browser history.
    /// </summary>
    public async Task GoForwardAsync()
    {
        await Page.GoForwardAsync();
    }

    /// <summary>
    /// Waits for a specified amount of time.
    /// </summary>
    public async Task WaitAsync(int milliseconds)
    {
        await Page.WaitForTimeoutAsync(milliseconds);
    }

    /// <summary>
    /// Waits for text to appear on the page.
    /// </summary>
    public async Task WaitForTextAsync(string text, int timeoutMs = 30000)
    {
        await Page.WaitForSelectorAsync($"text={text}", new PageWaitForSelectorOptions
        {
            Timeout = timeoutMs
        });
    }

    /// <summary>
    /// Waits for text to disappear from the page.
    /// </summary>
    public async Task WaitForTextToDisappearAsync(string text, int timeoutMs = 30000)
    {
        try
        {
            await Page.WaitForSelectorAsync($"text={text}", new PageWaitForSelectorOptions
            {
                State = WaitForSelectorState.Detached,
                Timeout = timeoutMs
            });
        }
        catch (TimeoutException)
        {
            // Text didn't disappear within timeout - this might be expected
        }
    }

    private string FormatAccessibilitySnapshot(AccessibilityNode? node, int indent = 0)
    {
        if (node == null) return "";

        var result = new List<string>();
        var indentStr = new string(' ', indent * 2);

        // Format the current node
        var nodeInfo = new List<string>();
        
        if (!string.IsNullOrEmpty(node.Role))
            nodeInfo.Add($"role={node.Role}");
        
        if (!string.IsNullOrEmpty(node.Name))
            nodeInfo.Add($"name=\"{node.Name}\"");
        
        if (!string.IsNullOrEmpty(node.Value))
            nodeInfo.Add($"value=\"{node.Value}\"");

        if (node.Checked.HasValue)
            nodeInfo.Add($"checked={node.Checked.Value}");

        if (node.Disabled.HasValue && node.Disabled.Value)
            nodeInfo.Add("disabled=true");

        if (node.Selected.HasValue && node.Selected.Value)
            nodeInfo.Add("selected=true");

        var nodeStr = nodeInfo.Count > 0 ? $"<{string.Join(" ", nodeInfo)}>" : "<>";
        result.Add($"{indentStr}{nodeStr}");

        // Add children
        if (node.Children != null)
        {
            foreach (var child in node.Children)
            {
                result.Add(FormatAccessibilitySnapshot(child, indent + 1));
            }
        }

        return string.Join("\n", result.Where(s => !string.IsNullOrEmpty(s)));
    }
}
